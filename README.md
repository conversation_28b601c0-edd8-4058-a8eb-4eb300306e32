# APISportsGamev2-FECMS

Frontend Content Management System for APISportsGame - A comprehensive sports management platform.

## 🏆 Project Overview

APISportsGamev2-FECMS is a modern, responsive frontend CMS built with Next.js 15 for managing sports-related data including tournaments, matches, teams, players, and user systems. It serves as the administrative interface for the APISportsGame backend API.

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **UI Library**: Ant Design
- **State Management**: 
  - <PERSON>ustand (Global state)
  - TanStack Query (Server state management)
- **API Integration**: Next.js reverse proxy to localhost:3000
- **Development Port**: 4000

## 📋 Features

### Core Modules

1. **User System Management**
   - Admin, Editor, Moderator role management
   - SystemUser authentication and profiles
   - Role-based access control

2. **User Register Management**
   - Registered users (Players, Fans) management
   - User profiles and statistics
   - Registration approval workflows

3. **Tournament Management**
   - Create, Read, Update, Delete tournaments
   - Tournament search and filtering
   - Tournament scheduling and organization

4. **Match Management**
   - Fixtures management (CRUD operations)
   - Match results and statistics
   - Search and filtering capabilities

5. **Dashboard**
   - Analytics and insights
   - Quick actions and shortcuts
   - System overview and statistics

## 🏗️ Architecture

### Modular Design
The project follows a modular architecture where each feature is self-contained:

```
src/modules/
├── user-system/     # SystemUser management
├── user-register/   # RegisteredUser management  
├── tournament/      # Tournament management
└── match/          # Match management
```

### API Integration
- All API calls are proxied through Next.js API routes for security
- Backend API runs on localhost:3000
- Frontend CMS runs on port 4000
- TanStack Query handles caching and synchronization

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+ 
- npm/yarn/pnpm
- APISportsGame backend API running on localhost:3000

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd APISportsGamev2-FECMS

# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:4000`

### Development Features
- **Hot Reload**: Automatic refresh on code changes
- **TypeScript**: Full type safety and IntelliSense
- **ESLint + Prettier**: Code formatting and linting
- **Authentication Disabled**: For easier development and testing

## 📁 Project Structure

```
src/
├── app/                    # Next.js 15 App Router
├── components/             # Shared components
├── modules/               # Feature modules
├── lib/                   # Utilities and configurations
├── hooks/                 # Shared React hooks
├── types/                 # Global TypeScript types
└── store/                # Zustand stores
```

## 🔧 Configuration

### Environment Variables
```env
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=APISportsGame CMS
```

### Next.js Configuration
- Custom port: 4000
- API proxy configuration for backend integration
- TypeScript strict mode enabled

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run integration tests
npm run test:integration

# Run all tests with coverage
npm run test:coverage
```

## 📚 Documentation

- **Development Rules**: `.augment-rules.md`
- **Project Structure**: `project_structure.txt`
- **Deployment Guide**: `DEPLOYMENT_GUIDE.md`
- **Module Completion Log**: `MODULE_COMPLETION_LOG.md`
- **Working Logs**: `LogWorking/` directory

## 🚀 Deployment

See `DEPLOYMENT_GUIDE.md` for detailed deployment instructions.

## 🤝 Contributing

1. Follow the development rules in `.augment-rules.md`
2. Create feature branches for new modules
3. Update documentation after completing features
4. Ensure all tests pass before submitting

## 📝 License

[License information to be added]

## 🔗 Related Projects

- **APISportsGame Backend**: Main API service
- **APISportsGame Mobile**: Mobile application
- **APISportsGame Web**: Public web interface

---

**Last Updated**: Initial project setup
**Version**: 1.0.0-alpha
**Status**: In Development
