# APISportsGamev2-FECMS Development Rules

## Project Overview
- **Project Name**: APISportsGamev2-FECMS (Frontend CMS for APISportsGame)
- **Tech Stack**: Next.js 15 + TypeScript + Ant Design + TanStack Query + Zustand
- **Port**: 4000 (not default 3000)
- **API Integration**: localhost:3000 via Next.js reverse proxy
- **Language**: English only for CMS interface
- **Development Phase**: Authentication disabled for easier testing

## Architecture Principles

### 1. Modular Structure
- Separate modules for each major feature
- Each module should be self-contained with its own components, hooks, types
- Avoid tight coupling between modules
- Use barrel exports (index.ts) for clean imports

### 2. Directory Structure Rules
```
src/
├── app/                    # Next.js 15 App Router
├── components/             # Shared components
├── modules/               # Feature modules
│   ├── user-system/       # SystemUser management (Admin/Editor/Moderator)
│   ├── user-register/     # RegisteredUser management
│   ├── tournament/        # Tournament management
│   └── match/            # Match management
├── lib/                   # Utilities, configs
├── hooks/                 # Shared hooks
├── types/                 # Global TypeScript types
└── store/                # Zustand stores
```

### 3. Naming Conventions
- **Files**: kebab-case (user-system.tsx)
- **Components**: PascalCase (UserSystemTable)
- **Variables/Functions**: camelCase (getUserList)
- **Constants**: UPPER_SNAKE_CASE (API_BASE_URL)
- **Types/Interfaces**: PascalCase with descriptive names (SystemUserType, TournamentCreateRequest)

### 4. Component Rules
- Use functional components with TypeScript
- Implement proper error boundaries
- Use React.memo for performance optimization when needed
- Prefer composition over inheritance
- Keep components small and focused (max 200 lines)

### 5. State Management
- **Zustand**: For global state (user auth, app settings)
- **TanStack Query**: For server state management (API calls, caching)
- **Local State**: useState for component-specific state
- Avoid prop drilling - use context or global state for deep data

### 6. API Integration Rules
- All API calls through Next.js API routes (reverse proxy)
- Use TanStack Query for all server interactions
- Implement proper error handling and loading states
- Use TypeScript interfaces for all API requests/responses
- Cache strategy: stale-while-revalidate for most data

### 7. Authentication Rules
- **Development Phase**: Disable mandatory login logic
- **Production Ready**: Use SystemUser endpoints (/system-auth/*)
- Store auth tokens securely
- Implement role-based access control (Admin/Editor/Moderator)

### 8. UI/UX Guidelines
- Use Ant Design components consistently
- Implement responsive design (mobile-first approach)
- Follow Ant Design design tokens for colors, spacing
- Provide loading states for all async operations
- Show meaningful error messages to users
- Implement proper form validation

## CMS Specific Features
- User System Management: Admin/Editor/Moderator roles
- User Register Management: Players, fans management
- Tournament Management: CRUD + search functionality
- Match Management: Fixtures CRUD + search functionality
- Dashboard with analytics and quick actions
- Responsive design for mobile and desktop usage
