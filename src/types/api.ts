/**
 * API Types for APISportsGame CMS
 * Defines TypeScript interfaces for API requests and responses
 */

// Base API Response Types
export interface BaseApiResponse {
  success: boolean;
  message?: string;
  statusCode?: number;
}

export interface ApiSuccessResponse<T = any> extends BaseApiResponse {
  success: true;
  data: T;
}

export interface ApiErrorResponse extends BaseApiResponse {
  success: false;
  error: string;
  details?: any;
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
}

// System Authentication Types
export interface SystemUser {
  id: string;
  username: string;
  email: string;
  role: 'Admin' | 'Editor' | 'Moderator';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: SystemUser;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role: 'Admin' | 'Editor' | 'Moderator';
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UpdateProfileRequest {
  email?: string;
  username?: string;
}

// Football Data Types
export interface League {
  id: number;
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  season: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Team {
  id: number;
  name: string;
  code?: string;
  country: string;
  founded?: number;
  logo?: string;
  venue?: {
    id: number;
    name: string;
    address?: string;
    city: string;
    capacity?: number;
    surface?: string;
    image?: string;
  };
  statistics?: {
    played: number;
    wins: number;
    draws: number;
    losses: number;
    goalsFor: number;
    goalsAgainst: number;
    points: number;
  };
}

export interface Fixture {
  id: number;
  externalId: string;
  date: string;
  timestamp: number;
  status: {
    long: string;
    short: string;
    elapsed?: number;
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo?: string;
    season: number;
    round: string;
  };
  teams: {
    home: {
      id: number;
      name: string;
      logo?: string;
      winner?: boolean;
    };
    away: {
      id: number;
      name: string;
      logo?: string;
      winner?: boolean;
    };
  };
  goals: {
    home?: number;
    away?: number;
  };
  score?: {
    halftime: {
      home?: number;
      away?: number;
    };
    fulltime: {
      home?: number;
      away?: number;
    };
    extratime?: {
      home?: number;
      away?: number;
    };
    penalty?: {
      home?: number;
      away?: number;
    };
  };
  venue?: {
    id?: number;
    name?: string;
    city?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface FixtureSearchParams extends PaginationParams {
  date?: string;
  dateFrom?: string;
  dateTo?: string;
  league?: number;
  team?: number;
  status?: string;
  season?: number;
}

export interface SyncStatus {
  isRunning: boolean;
  lastSync?: string;
  nextSync?: string;
  totalFixtures: number;
  successCount: number;
  errorCount: number;
  errors?: string[];
}

// Broadcast Links Types
export interface BroadcastLink {
  id: string;
  fixtureId: number;
  title: string;
  url: string;
  quality?: string;
  language?: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  fixture?: Fixture;
}

export interface CreateBroadcastLinkRequest {
  fixtureId: number;
  title: string;
  url: string;
  quality?: string;
  language?: string;
}

export interface UpdateBroadcastLinkRequest {
  title?: string;
  url?: string;
  quality?: string;
  language?: string;
  isActive?: boolean;
}

// Search and Filter Types
export interface SearchParams {
  q?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface LeagueSearchParams extends PaginationParams, SearchParams {
  country?: string;
  season?: number;
  isActive?: boolean;
}

export interface TeamSearchParams extends PaginationParams, SearchParams {
  country?: string;
  league?: number;
}

// Error Types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ApiErrorDetails {
  validationErrors?: ValidationError[];
  code?: string;
  timestamp?: string;
  path?: string;
}

// Health Check Types
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  api: {
    baseUrl: string;
    timeout: number;
  };
  backend: {
    status: 'healthy' | 'unhealthy' | 'unreachable';
    url: string;
    response?: any;
  };
  features: {
    authentication: boolean;
    footballData: boolean;
    broadcastLinks: boolean;
    fixtures: {
      authRequired: boolean;
    };
  };
}

// Request Context Types
export interface RequestContext {
  user?: SystemUser;
  token?: string;
  userAgent?: string;
  ip?: string;
  timestamp: string;
}
