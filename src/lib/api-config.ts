/**
 * API Configuration for APISportsGame CMS
 * Handles proxy configuration and API utilities
 */

// API Base Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// API Endpoints Configuration
export const API_ENDPOINTS = {
  // System Authentication (requires auth)
  SYSTEM_AUTH: {
    LOGIN: '/system-auth/login',
    PROFILE: '/system-auth/profile',
    LOGOUT: '/system-auth/logout',
    LOGOUT_ALL: '/system-auth/logout-all',
    CREATE_USER: '/system-auth/create-user',
    CHANGE_PASSWORD: '/system-auth/change-password',
    REFRESH: '/system-auth/refresh',
  },
  
  // Football Data (some endpoints no auth required)
  FOOTBALL: {
    LEAGUES: '/football/leagues',
    TEAMS: '/football/teams',
    FIXTURES: '/football/fixtures', // No auth required
    FIXTURES_SYNC: '/football/fixtures/sync',
    FIXTURES_SYNC_STATUS: '/football/fixtures/sync/status',
    FIXTURES_SYNC_DAILY: '/football/fixtures/sync/daily',
  },
  
  // Broadcast Links (requires auth)
  BROADCAST_LINKS: {
    BASE: '/broadcast-links',
    BY_FIXTURE: '/broadcast-links/fixture',
  },
} as const;

// HTTP Methods
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
} as const;

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  statusCode?: number;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
  details?: any;
}

// Request Configuration
export interface ProxyRequestConfig {
  method: string;
  url: string;
  headers?: Record<string, string>;
  body?: any;
  requiresAuth?: boolean;
}

// Authentication Configuration
export const AUTH_CONFIG = {
  TOKEN_HEADER: 'Authorization',
  TOKEN_PREFIX: 'Bearer',
  REFRESH_TOKEN_HEADER: 'X-Refresh-Token',
} as const;

// Endpoints that require authentication
export const PROTECTED_ENDPOINTS = [
  '/system-auth/profile',
  '/system-auth/logout',
  '/system-auth/logout-all',
  '/system-auth/create-user',
  '/system-auth/change-password',
  '/football/leagues',
  '/football/teams',
  '/football/fixtures/sync',
  '/broadcast-links',
] as const;

// Endpoints that don't require authentication
export const PUBLIC_ENDPOINTS = [
  '/system-auth/login',
  '/system-auth/refresh',
  '/football/fixtures', // Updated: No auth required
] as const;

/**
 * Check if an endpoint requires authentication
 */
export function requiresAuthentication(endpoint: string): boolean {
  // Check if it's explicitly in public endpoints
  if (PUBLIC_ENDPOINTS.some(publicEndpoint => endpoint.startsWith(publicEndpoint))) {
    return false;
  }
  
  // Check if it's in protected endpoints
  return PROTECTED_ENDPOINTS.some(protectedEndpoint => 
    endpoint.startsWith(protectedEndpoint)
  );
}

/**
 * Build full API URL
 */
export function buildApiUrl(endpoint: string): string {
  const baseUrl = API_CONFIG.BASE_URL.replace(/\/$/, '');
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
}

/**
 * Default headers for API requests
 */
export function getDefaultHeaders(): Record<string, string> {
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
}

/**
 * Get authentication headers if token is available
 */
export function getAuthHeaders(token?: string): Record<string, string> {
  if (!token) return {};
  
  return {
    [AUTH_CONFIG.TOKEN_HEADER]: `${AUTH_CONFIG.TOKEN_PREFIX} ${token}`,
  };
}
