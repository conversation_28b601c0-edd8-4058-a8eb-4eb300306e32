/**
 * API Utility Functions for Proxy Operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  API_CONFIG, 
  ApiResponse, 
  ApiError, 
  ProxyRequestConfig,
  buildApiUrl,
  getDefaultHeaders,
  getAuthHeaders,
  requiresAuthentication 
} from './api-config';

/**
 * Extract authentication token from request headers
 */
export function extractAuthToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7); // Remove 'Bearer ' prefix
}

/**
 * Create error response
 */
export function createErrorResponse(
  message: string, 
  statusCode: number = 500,
  error?: string
): NextResponse<ApiError> {
  return NextResponse.json(
    {
      message,
      statusCode,
      error: error || 'Internal Server Error',
    },
    { status: statusCode }
  );
}

/**
 * Create success response
 */
export function createSuccessResponse<T>(
  data: T,
  statusCode: number = 200,
  message?: string
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      data,
      message,
      statusCode,
    },
    { status: statusCode }
  );
}

/**
 * Validate request method
 */
export function validateMethod(
  request: NextRequest, 
  allowedMethods: string[]
): boolean {
  return allowedMethods.includes(request.method);
}

/**
 * Parse request body safely
 */
export async function parseRequestBody(request: NextRequest): Promise<any> {
  try {
    const contentType = request.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      return await request.json();
    }
    
    if (contentType?.includes('application/x-www-form-urlencoded')) {
      const formData = await request.formData();
      const body: Record<string, any> = {};
      formData.forEach((value, key) => {
        body[key] = value;
      });
      return body;
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing request body:', error);
    return null;
  }
}

/**
 * Build proxy request configuration
 */
export async function buildProxyConfig(
  request: NextRequest,
  targetEndpoint: string
): Promise<ProxyRequestConfig> {
  const body = await parseRequestBody(request);
  const token = extractAuthToken(request);
  const requiresAuth = requiresAuthentication(targetEndpoint);
  
  // Build headers
  const headers = {
    ...getDefaultHeaders(),
    ...(requiresAuth && token ? getAuthHeaders(token) : {}),
  };
  
  // Copy relevant headers from original request
  const relevantHeaders = ['user-agent', 'accept-language', 'x-forwarded-for'];
  relevantHeaders.forEach(headerName => {
    const headerValue = request.headers.get(headerName);
    if (headerValue) {
      headers[headerName] = headerValue;
    }
  });
  
  return {
    method: request.method,
    url: buildApiUrl(targetEndpoint),
    headers,
    body: body ? JSON.stringify(body) : undefined,
    requiresAuth,
  };
}

/**
 * Execute proxy request to backend API
 */
export async function executeProxyRequest(
  config: ProxyRequestConfig
): Promise<Response> {
  const { method, url, headers, body } = config;
  
  try {
    const response = await fetch(url, {
      method,
      headers,
      body,
      signal: AbortSignal.timeout(API_CONFIG.TIMEOUT),
    });
    
    return response;
  } catch (error) {
    console.error('Proxy request failed:', error);
    throw new Error(`Proxy request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Handle proxy response and convert to NextResponse
 */
export async function handleProxyResponse(response: Response): Promise<NextResponse> {
  try {
    const contentType = response.headers.get('content-type');
    
    // Handle JSON responses
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      return NextResponse.json(data, { 
        status: response.status,
        statusText: response.statusText,
      });
    }
    
    // Handle text responses
    if (contentType?.includes('text/')) {
      const text = await response.text();
      return new NextResponse(text, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          'content-type': contentType,
        },
      });
    }
    
    // Handle binary responses
    const buffer = await response.arrayBuffer();
    return new NextResponse(buffer, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'content-type': contentType || 'application/octet-stream',
      },
    });
    
  } catch (error) {
    console.error('Error handling proxy response:', error);
    return createErrorResponse(
      'Failed to process response from backend API',
      500
    );
  }
}

/**
 * Main proxy handler function
 */
export async function handleProxyRequest(
  request: NextRequest,
  targetEndpoint: string,
  allowedMethods: string[] = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']
): Promise<NextResponse> {
  try {
    // Validate HTTP method
    if (!validateMethod(request, allowedMethods)) {
      return createErrorResponse(
        `Method ${request.method} not allowed`,
        405,
        'Method Not Allowed'
      );
    }
    
    // Check authentication if required
    const requiresAuth = requiresAuthentication(targetEndpoint);
    if (requiresAuth) {
      const token = extractAuthToken(request);
      if (!token) {
        return createErrorResponse(
          'Authentication required',
          401,
          'Unauthorized'
        );
      }
    }
    
    // Build proxy configuration
    const proxyConfig = await buildProxyConfig(request, targetEndpoint);
    
    // Execute proxy request
    const response = await executeProxyRequest(proxyConfig);
    
    // Handle and return response
    return await handleProxyResponse(response);
    
  } catch (error) {
    console.error('Proxy handler error:', error);
    return createErrorResponse(
      error instanceof Error ? error.message : 'Internal server error',
      500
    );
  }
}

/**
 * Log proxy request for debugging
 */
export function logProxyRequest(
  request: NextRequest,
  targetEndpoint: string,
  config: ProxyRequestConfig
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[PROXY] ${request.method} ${request.url} -> ${config.url}`);
    console.log(`[PROXY] Auth required: ${config.requiresAuth}`);
    console.log(`[PROXY] Headers:`, Object.keys(config.headers || {}));
  }
}
