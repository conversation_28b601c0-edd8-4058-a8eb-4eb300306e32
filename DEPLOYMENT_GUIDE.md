# APISportsGamev2-FECMS Deployment Guide

This guide provides comprehensive instructions for deploying the APISportsGamev2-FECMS application to various environments.

## 📋 Prerequisites

### System Requirements
- Node.js 18.0 or higher
- npm 9.0 or higher (or yarn/pnpm equivalent)
- Git
- APISportsGame backend API accessible

### Environment Setup
- Production server with sufficient resources
- Domain name (optional)
- SSL certificate (recommended for production)
- Reverse proxy server (nginx/Apache) - optional

## 🔧 Environment Configuration

### Environment Variables

Create appropriate `.env` files for each environment:

#### Development (.env.local)
```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_API_TIMEOUT=10000

# Application Configuration
NEXT_PUBLIC_APP_NAME=APISportsGame CMS
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# Development Settings
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_AUTH_DISABLED=true

# Port Configuration
PORT=4000
```

#### Production (.env.production)
```env
# API Configuration
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_API_TIMEOUT=15000

# Application Configuration
NEXT_PUBLIC_APP_NAME=APISportsGame CMS
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=production

# Production Settings
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_AUTH_DISABLED=false

# Security
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=https://cms.yourdomain.com

# Port Configuration
PORT=4000
```

## 🚀 Deployment Methods

### Method 1: Traditional Server Deployment

#### 1. Server Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Create application directory
sudo mkdir -p /var/www/apisportsgame-cms
sudo chown $USER:$USER /var/www/apisportsgame-cms
```

#### 2. Application Deployment
```bash
# Clone repository
cd /var/www/apisportsgame-cms
git clone <repository-url> .

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'apisportsgame-cms',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/apisportsgame-cms',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 4000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
EOF

# Create logs directory
mkdir -p logs

# Start application with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 3. Nginx Configuration (Optional)
```nginx
server {
    listen 80;
    server_name cms.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Method 2: Docker Deployment

#### 1. Create Dockerfile
```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

COPY . .
RUN npm run build

# Production stage
FROM node:18-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

USER nextjs

EXPOSE 4000

ENV PORT 4000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### 2. Docker Compose Configuration
```yaml
version: '3.8'

services:
  apisportsgame-cms:
    build: .
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://api:3000
    depends_on:
      - api
    restart: unless-stopped
    
  api:
    image: apisportsgame-api:latest
    ports:
      - "3000:3000"
    restart: unless-stopped

networks:
  default:
    name: apisportsgame-network
```

#### 3. Deploy with Docker
```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f apisportsgame-cms

# Update deployment
docker-compose pull
docker-compose up -d --force-recreate
```

### Method 3: Vercel Deployment

#### 1. Vercel Configuration (vercel.json)
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "env": {
    "NEXT_PUBLIC_API_URL": "https://api.yourdomain.com"
  },
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

#### 2. Deploy to Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

## 🔒 Security Considerations

### 1. Environment Security
- Never commit `.env` files to version control
- Use secure secret management for production
- Regularly rotate API keys and secrets
- Enable HTTPS in production

### 2. API Security
- Configure CORS properly
- Implement rate limiting
- Use API authentication tokens
- Validate all input data

### 3. Application Security
- Keep dependencies updated
- Use security headers
- Implement CSP (Content Security Policy)
- Regular security audits

## 📊 Monitoring & Maintenance

### 1. Application Monitoring
```bash
# PM2 monitoring
pm2 monit

# View logs
pm2 logs apisportsgame-cms

# Restart application
pm2 restart apisportsgame-cms

# Update application
git pull
npm ci --only=production
npm run build
pm2 restart apisportsgame-cms
```

### 2. Health Checks
- Monitor application uptime
- Check API connectivity
- Monitor resource usage
- Set up alerting for failures

### 3. Backup Strategy
- Regular database backups (if applicable)
- Code repository backups
- Configuration file backups
- Disaster recovery plan

## 🚨 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Find process using port 4000
lsof -i :4000

# Kill process
kill -9 <PID>
```

#### 2. Build Failures
```bash
# Clear cache and rebuild
rm -rf .next node_modules
npm install
npm run build
```

#### 3. API Connection Issues
- Verify API URL in environment variables
- Check network connectivity
- Verify API service status
- Check firewall settings

## 📝 Deployment Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] Dependencies installed
- [ ] Application builds successfully
- [ ] Tests pass
- [ ] Security review completed

### Deployment
- [ ] Application deployed
- [ ] Health checks pass
- [ ] API connectivity verified
- [ ] SSL certificate installed (production)
- [ ] Monitoring configured

### Post-deployment
- [ ] Functionality testing
- [ ] Performance testing
- [ ] Security testing
- [ ] Documentation updated
- [ ] Team notified

---

**Last Updated**: Initial creation
**Version**: 1.0.0
**Maintainer**: Development Team
