[{"name": "hot-reloader", "duration": 32, "timestamp": 34896923385, "id": 3, "tags": {"version": "15.1.8"}, "startTime": 1748095131360, "traceId": "47bb5a4a795fe39d"}, {"name": "setup-dev-bundler", "duration": 526546, "timestamp": 34896794180, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748095131230, "traceId": "47bb5a4a795fe39d"}, {"name": "run-instrumentation-hook", "duration": 11, "timestamp": 34897349239, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748095131785, "traceId": "47bb5a4a795fe39d"}, {"name": "start-dev-server", "duration": 831386, "timestamp": 34896526585, "id": 1, "tags": {"cpus": "32", "platform": "linux", "memory.freeMem": "43152916480", "memory.totalMem": "49268174848", "memory.heapSizeLimit": "24683479040", "memory.rss": "375005184", "memory.heapTotal": "97947648", "memory.heapUsed": "71218392"}, "startTime": 1748095130963, "traceId": "47bb5a4a795fe39d"}, {"name": "compile-path", "duration": 2298104, "timestamp": 34927157372, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748095161594, "traceId": "47bb5a4a795fe39d"}, {"name": "ensure-page", "duration": 2298810, "timestamp": 34927157006, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748095161593, "traceId": "47bb5a4a795fe39d"}]